/* ========================================
   OK Tyr Website - Main SCSS Entry Point
   Mobile-first responsive design system
   ======================================== */
/* ========================================
   Foundation Styles - Reset & Base Setup
   ======================================== */
/* CSS Reset & Box Model */
* {
  box-sizing: border-box;
}

/* Base HTML & Body Setup */
html {
  font-size: 16px;
  line-height: 1.5;
}

body {
  margin: 0;
  padding: 8px;
  /* Typography Foundation */
  font-family: "Inter", sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.6;
  color: #1a1a1a;
  /* Font Rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Background */
  background-color: #eef2f6;
}

/* Layout Container */
.container {
  max-width: 1440px;
  margin: 40px auto;
}

/* ========================================
   Typography System - Inter Font Scale
   Based on 1.25 (Major Third) scale with optical adjustments
   ======================================== */
/* Heading Hierarchy */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  margin: 0;
  /* Remove margin from last child */
}
h1:last-child, h2:last-child, h3:last-child, h4:last-child, h5:last-child, h6:last-child {
  margin-bottom: 0;
}

h1 {
  font-size: 2.25rem;
  line-height: 1.2;
  font-weight: 700;
  letter-spacing: -0.01em;
  margin-bottom: 1.5rem;
}

h2 {
  font-size: 1.875rem;
  line-height: 1.25;
  letter-spacing: -0.01em;
  margin-bottom: 1.25rem;
}

h3 {
  font-size: 1.5rem;
  line-height: 1.3;
  letter-spacing: -0.005em;
  margin-bottom: 1rem;
}

h4 {
  font-size: 1.25rem;
  line-height: 1.35;
  margin-bottom: 1rem;
}

h5 {
  font-size: 1.125rem;
  line-height: 1.4;
  margin-bottom: 0.75rem;
}

h6 {
  font-size: 1rem;
  line-height: 1.45;
  margin-bottom: 0.75rem;
}

/* Body Text */
p {
  font-size: 1rem;
  line-height: 1.6;
  font-weight: 400;
  margin: 0 0 1rem 0;
}
p:last-child {
  margin-bottom: 0;
}

/* ========================================
   Responsive Typography Adjustments
   ======================================== */
/* Tablet & Mobile Adjustments */
@media (max-width: 768px) {
  h1 {
    font-size: 1.875rem;
    line-height: 1.2;
  }
  h2 {
    font-size: 1.5rem;
    line-height: 1.25;
  }
  h3 {
    font-size: 1.25rem;
    line-height: 1.3;
  }
  h4 {
    font-size: 1.125rem;
    line-height: 1.35;
  }
}
/* Small Mobile Adjustments */
@media (max-width: 480px) {
  h1 {
    font-size: 1.5rem;
    line-height: 1.25;
  }
  h2 {
    font-size: 1.25rem;
    line-height: 1.3;
  }
  h3 {
    font-size: 1.125rem;
    line-height: 1.35;
  }
}
/* ========================================
   Button System - Consistent Interactive Elements
   ======================================== */
/* Base Button Foundation */
button, .button {
  /* Layout & Positioning */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  /* Visual Styling */
  border: none;
  border-radius: 20rem;
  background: none;
  outline: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  /* Typography */
  font-family: "Inter", sans-serif;
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  /* Interaction */
  cursor: pointer;
  transform: translateY(0) scale(1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* Hover Effects */
  /* Active State */
  /* Focus State */
  /* Disabled State */
}
button:hover, .button:hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}
button:active, .button:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.1s ease;
}
button:focus, .button:focus {
  outline: 2px solid rgba(1, 100, 73, 0.5);
  outline-offset: 2px;
}
button:disabled, button.disabled, .button:disabled, .button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}
button:disabled:hover, button.disabled:hover, .button:disabled:hover, .button.disabled:hover {
  background-color: initial;
  color: initial;
}

/* ========================================
   Button Variants - Brand Color System
   ======================================== */
/* Primary Button - Brand Red */
.button-primary,
button.primary {
  background-color: #9C2B32;
  color: white;
}
.button-primary:hover,
button.primary:hover {
  background-color: #cfe7cb;
  color: #016449;
  box-shadow: 0 4px 12px rgba(156, 43, 50, 0.3), 0 8px 24px rgba(156, 43, 50, 0.15);
}

/* Secondary Button - Brand Green */
.button-secondary,
button.secondary {
  background-color: #cfe7cb;
  color: #016449;
}
.button-secondary:hover,
button.secondary:hover {
  background-color: #016449;
  color: white;
  box-shadow: 0 4px 12px rgba(1, 100, 73, 0.3), 0 8px 24px rgba(1, 100, 73, 0.15);
}

/* Menu CTA Button - Black with Red Hover */
.button-menu,
button.menu {
  background-color: #000000;
  color: white;
}
.button-menu:hover,
button.menu:hover {
  background-color: #9C2B32;
  color: white;
  box-shadow: 0 4px 12px rgba(156, 43, 50, 0.4), 0 8px 24px rgba(156, 43, 50, 0.2);
}

/* ========================================
   Button Sizes & Modifiers
   ======================================== */
/* Size Variants */
.button-sm,
button.sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.button-lg,
button.lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

/* Outline Variants */
.button-outline {
  background-color: transparent;
  border: 2px solid #016449;
  color: #016449;
  box-shadow: none;
}
.button-outline:hover {
  background-color: #016449;
  color: white;
  box-shadow: 0 4px 12px rgba(1, 100, 73, 0.3), 0 8px 24px rgba(1, 100, 73, 0.15);
  border-color: #016449;
}
.button-outline:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.1s ease;
}

.button-outline-primary {
  background-color: transparent;
  border: 2px solid #9C2B32;
  color: #9C2B32;
  box-shadow: none;
}
.button-outline-primary:hover {
  background-color: #9C2B32;
  color: white;
  box-shadow: 0 4px 12px rgba(156, 43, 50, 0.3), 0 8px 24px rgba(156, 43, 50, 0.15);
  border-color: #9C2B32;
}

/* Layout Modifiers */
.button-full,
button.full {
  width: 100%;
}

.button-icon,
button.icon {
  gap: 0.5rem;
}

/* ========================================
   Navigation System - Floating Frosted Glass Design
   ======================================== */
/* Navigation Container - Fixed Positioning */
.navbar-container {
  /* Layout & Positioning */
  position: fixed;
  top: 16px;
  left: 0;
  right: 0;
  z-index: 100;
  /* Container Sizing */
  max-width: 1440px;
  width: 100%;
  margin: 0 auto;
  padding: 0 80px;
  /* Flexbox Layout */
  display: flex;
  justify-content: center;
  align-items: center;
  /* Smooth Show/Hide Animation */
  transform: translateY(0);
  transition: transform 0.4s ease-in-out;
  /* Navigation States */
}
.navbar-container.nav-hidden {
  transform: translateY(-200%);
}
.navbar-container.nav-visible {
  transform: translateY(0);
}

/* Main Navigation Bar */
.navbar {
  /* Layout & Positioning */
  position: relative;
  width: 100%;
  max-width: 1250px;
  display: flex;
  align-items: center;
  padding: 16px;
  /* Visual Styling */
  border-radius: 10rem;
  color: white;
  transition: all 0.4s ease;
  /* Fallback Background (for unsupported browsers) */
  background: rgba(1, 100, 73, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  /* Mobile Expanded State */
}
.navbar.nav-mobile-expanded {
  /* Layout Changes */
  border-radius: 1.5rem;
  flex-direction: column;
  align-items: stretch;
  height: 100vh;
  max-width: none;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  transform-origin: center top;
  /* Header Elements Positioning */
  /* Mobile Content Area */
}
.navbar.nav-mobile-expanded .nav-logo,
.navbar.nav-mobile-expanded .nav-mobile-toggle {
  position: absolute;
  z-index: 10;
}
.navbar.nav-mobile-expanded .nav-logo {
  top: 16px;
  left: 16px;
}
.navbar.nav-mobile-expanded .nav-mobile-toggle {
  top: 16px;
  right: 16px;
}
.navbar.nav-mobile-expanded .nav-mobile-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 4rem 1rem 1rem;
  overflow-y: auto;
}
.navbar.nav-mobile-expanded .nav-mobile-items {
  flex: 1;
}

/* ========================================
   Frosted Glass Effect - Enhanced Visual Design
   ======================================== */
@supports ((-webkit-backdrop-filter: blur(16px)) or (backdrop-filter: blur(16px))) or (-webkit-backdrop-filter: blur(16px)) {
  .navbar {
    /* Enhanced Background with Brand Tint */
    background: rgba(1, 100, 73, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.1);
    /* Inset Border Effect */
    box-shadow: inset 0px 0px 0px 1px rgba(255, 255, 255, 0.1), 0 2px 8px rgba(0, 0, 0, 0.15);
    /* Frosted Glass Backdrop Filter */
    -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);
    backdrop-filter: blur(16px) saturate(140%) brightness(90%);
    /* Enhanced Mobile Expanded State */
  }
  .navbar.nav-mobile-expanded {
    background: rgba(0, 0, 0, 0.6);
    -webkit-backdrop-filter: blur(20px) saturate(150%) brightness(80%);
    backdrop-filter: blur(20px) saturate(150%) brightness(80%);
  }
}
/* ========================================
   Navigation Content Layout
   ======================================== */
/* Logo Section */
.nav-logo {
  flex: 0 0 auto;
  max-width: 40px;
  max-height: 40px;
}
.nav-logo img {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
     object-fit: contain;
}

/* Navigation Items Collection */
.nav-collection {
  display: flex;
  align-items: center;
  flex: 1 1 auto;
  justify-content: center;
}

/* Call-to-Action Section */
.nav-cta {
  flex: 0 0 auto;
}
.nav-cta a {
  /* Button Menu Styles */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #000000;
  color: white;
  padding: 8px 16px;
  border: none;
  border-radius: 20rem;
  font-family: "Inter", sans-serif;
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(0) scale(1);
}
.nav-cta a:hover {
  background-color: #9C2B32;
  color: white;
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 12px rgba(156, 43, 50, 0.4), 0 8px 24px rgba(156, 43, 50, 0.2);
}
.nav-cta a:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.1s ease;
}
.nav-cta a:focus {
  outline: 2px solid rgba(1, 100, 73, 0.5);
  outline-offset: 2px;
}

/* Navigation Item Container */
.nav-item {
  position: relative;
}

/* Override Global Button Styles for Navbar */
.navbar button {
  box-shadow: none;
}

/* ========================================
   Navigation Links & Interactive Elements
   ======================================== */
/* Base Navigation Links & Toggles */
.nav-link,
.nav-toggle {
  /* Layout */
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  /* Reset Styles */
  background: none;
  border: none;
  text-decoration: none;
  color: inherit;
  /* Typography */
  font-size: 1rem;
  font-weight: 500;
  /* Visual */
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  /* Override Global Button Styles */
  box-shadow: none !important;
  transform: none !important;
  /* Hover State */
  /* Active State */
  /* Focus State */
}
.nav-link:hover,
.nav-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: none !important;
  box-shadow: none !important;
}
.nav-link:active,
.nav-toggle:active {
  transform: none !important;
  box-shadow: none !important;
}
.nav-link:focus,
.nav-toggle:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.nav-toggle .nav-arrow,
.nav-toggle-level2 .nav-arrow {
  transition: all 0.3s ease;
  opacity: 1;
}
.nav-toggle[aria-expanded=true] .nav-arrow,
.nav-toggle-level2[aria-expanded=true] .nav-arrow {
  opacity: 0;
  transform: scale(0.8);
}
.nav-toggle[aria-expanded=true]::after,
.nav-toggle-level2[aria-expanded=true]::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 2px;
  background-color: currentColor;
  border-radius: 1px;
  opacity: 1;
  transform: scale(1);
  transition: all 0.3s ease 0.1s;
  right: 0.5rem;
}
.nav-toggle::after,
.nav-toggle-level2::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 2px;
  background-color: currentColor;
  border-radius: 1px;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
  right: 0.5rem;
}

.nav-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 200px;
  background: rgba(0, 0, 0, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  padding: 0.5rem 0;
  margin-top: 0.5rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  z-index: 1000;
}
@supports ((-webkit-backdrop-filter: blur(16px)) or (backdrop-filter: blur(16px))) or (-webkit-backdrop-filter: blur(16px)) {
  .nav-dropdown {
    -webkit-backdrop-filter: blur(16px) saturate(140%) brightness(90%);
    backdrop-filter: blur(16px) saturate(140%) brightness(90%);
  }
}
.nav-item:hover .nav-dropdown, .nav-item.nav-active .nav-dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.nav-dropdown-level2 {
  top: 0;
  left: 100%;
  margin-top: 0;
  margin-left: 0.5rem;
}

.nav-dropdown-item {
  position: relative;
}

.nav-dropdown-link,
.nav-toggle-level2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: inherit;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s ease;
  box-shadow: none !important;
  transform: none !important;
  border-radius: 0;
  font-weight: inherit;
  font-family: inherit;
}
.nav-dropdown-link:hover,
.nav-toggle-level2:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: none !important;
  box-shadow: none !important;
}
.nav-dropdown-link:active,
.nav-toggle-level2:active {
  transform: none !important;
  box-shadow: none !important;
}
.nav-dropdown-link:focus,
.nav-toggle-level2:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: -2px;
  background-color: rgba(255, 255, 255, 0.1);
}

/* This section is now handled above in the reorganized .nav-cta section */
.nav-mobile-toggle {
  display: none;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  color: inherit;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s ease;
  margin-left: auto;
  box-shadow: none !important;
  transform: none !important;
  font-size: inherit;
  font-weight: inherit;
  font-family: inherit;
}
.nav-mobile-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: none !important;
  box-shadow: none !important;
}
.nav-mobile-toggle:active {
  transform: none !important;
  box-shadow: none !important;
}
.nav-mobile-toggle:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.hamburger-icon {
  display: flex;
  flex-direction: column;
  gap: 3px;
  width: 18px;
  height: 14px;
  order: 2;
}

.hamburger-line {
  width: 100%;
  height: 2px;
  background-color: currentColor;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.nav-mobile-toggle-text {
  font-size: 0.9rem;
  font-weight: 500;
  order: 1;
}

.nav-mobile-toggle[aria-expanded=true] .hamburger-line:nth-child(1) {
  opacity: 0;
  transform: translateY(5px);
}
.nav-mobile-toggle[aria-expanded=true] .hamburger-line:nth-child(2) {
  transform: scaleX(1);
}
.nav-mobile-toggle[aria-expanded=true] .hamburger-line:nth-child(3) {
  opacity: 0;
  transform: translateY(-5px);
}

.nav-mobile-content {
  display: none;
  flex-direction: column;
  flex: 1;
  width: 100%;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.4s ease 0.2s;
}
.nav-mobile-expanded .nav-mobile-content {
  display: flex;
  opacity: 1;
  transform: translateY(0);
}

.nav-mobile-items {
  flex: 1;
  overflow-y: auto;
}

.nav-mobile-item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 0 1rem;
  opacity: 0;
  transform: translateY(10px);
  animation: none;
}
.nav-mobile-expanded .nav-mobile-item {
  animation: fadeInUp 0.4s ease forwards;
}
.nav-mobile-expanded .nav-mobile-item:nth-child(1) {
  animation-delay: 0.3s;
}
.nav-mobile-expanded .nav-mobile-item:nth-child(2) {
  animation-delay: 0.4s;
}
.nav-mobile-expanded .nav-mobile-item:nth-child(3) {
  animation-delay: 0.5s;
}
.nav-mobile-expanded .nav-mobile-item:nth-child(4) {
  animation-delay: 0.6s;
}
.nav-mobile-expanded .nav-mobile-item:nth-child(5) {
  animation-delay: 0.7s;
}
.nav-mobile-expanded .nav-mobile-item:nth-child(6) {
  animation-delay: 0.8s;
}
.nav-mobile-expanded .nav-mobile-item:nth-child(7) {
  animation-delay: 0.9s;
}
.nav-mobile-expanded .nav-mobile-item:nth-child(8) {
  animation-delay: 1s;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.nav-mobile-item-header,
.nav-mobile-subitem-header {
  display: flex;
  align-items: center;
  width: 100%;
}

.nav-mobile-item-header .nav-mobile-link,
.nav-mobile-subitem-header .nav-mobile-link {
  flex: 1;
  padding: 1rem 0;
  text-decoration: none;
  color: inherit;
  font-size: 1.1rem;
  font-weight: 500;
  transition: color 0.2s ease;
}
.nav-mobile-item-header .nav-mobile-link:hover,
.nav-mobile-subitem-header .nav-mobile-link:hover {
  color: rgba(255, 255, 255, 0.8);
}
.nav-mobile-item-header .nav-mobile-link:focus,
.nav-mobile-subitem-header .nav-mobile-link:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.nav-mobile-toggle-item,
.nav-mobile-toggle-subitem {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: none;
  border: none;
  cursor: pointer;
  color: inherit;
  border-radius: 0.5rem;
  transition: background-color 0.2s ease;
  padding: 0 !important;
  box-shadow: none !important;
  transform: none !important;
  font-size: inherit;
  font-weight: inherit;
  font-family: inherit;
}
.nav-mobile-toggle-item:hover,
.nav-mobile-toggle-subitem:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: none !important;
  box-shadow: none !important;
}
.nav-mobile-toggle-item:active,
.nav-mobile-toggle-subitem:active {
  transform: none !important;
  box-shadow: none !important;
}
.nav-mobile-toggle-item:focus,
.nav-mobile-toggle-subitem:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.nav-mobile-toggle-item .nav-arrow,
.nav-mobile-toggle-subitem .nav-arrow {
  transition: all 0.3s ease;
  opacity: 1;
}
.nav-mobile-toggle-item[aria-expanded=true] .nav-arrow,
.nav-mobile-toggle-subitem[aria-expanded=true] .nav-arrow {
  opacity: 0;
  transform: scale(0.8);
}
.nav-mobile-toggle-item[aria-expanded=true]::after,
.nav-mobile-toggle-subitem[aria-expanded=true]::after {
  content: "";
  position: absolute;
  width: 12px;
  height: 2px;
  background-color: currentColor;
  border-radius: 1px;
  opacity: 1;
  transform: scale(1);
  transition: all 0.3s ease 0.1s;
}
.nav-mobile-toggle-item::after,
.nav-mobile-toggle-subitem::after {
  content: "";
  position: absolute;
  width: 12px;
  height: 2px;
  background-color: currentColor;
  border-radius: 1px;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.nav-mobile-link {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 1rem 0;
  text-decoration: none;
  color: inherit;
  font-size: 1.1rem;
  font-weight: 500;
  transition: color 0.2s ease;
}
.nav-mobile-link:hover {
  color: rgba(255, 255, 255, 0.8);
}
.nav-mobile-link:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.nav-mobile-link-level1 {
  font-size: 1.2rem;
  font-weight: 600;
}

.nav-mobile-link-level2,
.nav-mobile-subitem-header .nav-mobile-link {
  padding-left: 1rem;
  font-size: 1rem;
  font-weight: 400;
}

.nav-mobile-link-level3 {
  padding-left: 2rem;
  font-size: 0.9rem;
  font-weight: 400;
}

.nav-mobile-submenu,
.nav-mobile-subsubmenu {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}
.nav-mobile-submenu.nav-mobile-submenu-active,
.nav-mobile-subsubmenu.nav-mobile-submenu-active {
  max-height: 500px;
}

.nav-mobile-subitem {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.nav-mobile-cta {
  margin-top: auto;
  padding: 1rem;
  width: 100%;
  opacity: 0;
  text-align: center;
  transform: translateY(20px);
  animation: none;
}
.nav-mobile-expanded .nav-mobile-cta {
  animation: fadeInUp 0.4s ease 1.1s forwards;
}

.nav-mobile-cta-button {
  /* Button Menu + Full Width Styles */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #000000;
  color: white;
  padding: 16px 24px;
  border: none;
  border-radius: 20rem;
  font-family: "Inter", sans-serif;
  font-size: 1rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(0) scale(1);
  width: 100%;
}
.nav-mobile-cta-button:hover {
  background-color: #9C2B32;
  color: white;
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 12px rgba(156, 43, 50, 0.4), 0 8px 24px rgba(156, 43, 50, 0.2);
}
.nav-mobile-cta-button:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.1s ease;
}
.nav-mobile-cta-button:focus {
  outline: 2px solid rgba(1, 100, 73, 0.5);
  outline-offset: 2px;
}

@media (max-width: 1600px) {
  .navbar-container {
    padding: 0 20px;
  }
}
@media (max-width: 1200px) {
  .nav-cta {
    display: none;
  }
}
@media (max-width: 1024px) {
  .nav-desktop {
    display: none;
  }
  .nav-mobile-toggle {
    display: flex;
  }
  .navbar {
    justify-content: space-between;
  }
  .navbar .nav-logo {
    flex: 0 0 auto;
  }
  .navbar.nav-mobile-expanded {
    position: fixed;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    max-width: none;
    width: auto;
    height: auto;
    z-index: 1000;
  }
  .navbar-container.nav-expanded {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 20px;
    z-index: 1000;
  }
}
@media (max-width: 768px) {
  .navbar-container {
    padding: 0 20px;
  }
  nav {
    padding: 12px;
  }
}
@media (min-width: 769px) {
  .nav-item:hover .nav-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
  .nav-item .nav-dropdown-item:hover .nav-dropdown-level2 {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}
.nav-dropdown {
  animation-duration: 0.2s;
  animation-timing-function: ease-out;
  animation-fill-mode: both;
}
.nav-dropdown.nav-dropdown-level2 {
  animation-delay: 0.1s;
}

.nav-toggle:focus,
.nav-toggle-level2:focus,
.nav-dropdown-link:focus {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
  background-color: rgba(255, 255, 255, 0.15);
}

/* ========================================
   Hero Section - Main landing area
   ======================================== */
.hero {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  /* Hero Content Container */
  /* Call-to-Action Section */
}
.hero h1 {
  font-size: 3em;
}
@media (max-width: 768px) {
  .hero h1 {
    font-size: 2em;
  }
}
.hero-content {
  height: 90vh;
  max-height: 800px;
  min-height: 600px;
  max-width: 1440px;
  width: 100%;
  position: relative;
  overflow: hidden;
  border-radius: 24px;
  padding: 2rem;
  /* Layout */
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-start;
  /* Background & Visual Effects */
  background-size: cover !important;
  background-position: center center !important;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%), linear-gradient(0deg, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), linear-gradient(0deg, #016449, #016449);
  background-blend-mode: normal, normal, color, normal, normal;
  /* Typography */
  color: white;
}
.hero-cta {
  margin-top: 2rem;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* ========================================
   Banner Content Area
   ======================================== */
.banner {
  width: 50vw;
  padding: 2rem;
  /* Typography Styles - inherit from global styles */
  /* Mobile Responsive */
}
@media (max-width: 768px) {
  .banner {
    width: 100%;
    padding: 0;
  }
}

/* Highlighted Updates Section - 2:1 Layout with responsive mobile-first approach */
.highlighted-updates {
  display: grid;
  gap: 2rem;
  margin-bottom: 3rem;
  /* Mobile: Stack vertically with calendar first */
  grid-template-columns: 1fr;
  grid-template-areas: "calendar" "news";
  /* Desktop: Side by side with 2:1 ratio */
}
@media (min-width: 768px) {
  .highlighted-updates {
    grid-template-columns: 2fr 1fr;
    grid-template-areas: "news calendar";
  }
}
.highlighted-updates h2 {
  grid-column: 1/-1;
  margin-bottom: 1.5rem;
}

/* News Section - Featured article with overlay */
.highlighted-updates-news {
  grid-area: news;
}
.highlighted-updates-news article {
  position: relative;
  height: 100%;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.highlighted-updates-news article:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}
.highlighted-updates-news article a {
  display: block;
  text-decoration: none;
  color: inherit;
  height: 100%;
  /* Fallback for missing images */
}
.highlighted-updates-news article a > div {
  position: relative;
  min-height: 300px;
  height: 100%;
}
@media (min-width: 768px) {
  .highlighted-updates-news article a > div {
    min-height: 400px;
  }
}
.highlighted-updates-news article a img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}
.highlighted-updates-news article a > div > div:first-child:not(.highlighted-news-content) {
  background: #016449;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

/* News content overlay */
.highlighted-news-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 2rem;
}
.highlighted-news-content time {
  display: block;
  font-size: 0.875rem;
  opacity: 0.9;
  margin-bottom: 0.5rem;
  font-weight: 500;
}
.highlighted-news-content h2 {
  margin: 0;
  font-size: 1.5rem;
  line-height: 1.3;
}
@media (min-width: 768px) {
  .highlighted-news-content h2 {
    font-size: 1.875rem;
  }
}
.highlighted-news-content p {
  margin: 0.75rem 0 0 0;
  opacity: 0.9;
  line-height: 1.5;
}

/* Calendar Section - Stacked event layout */
.highlighted-updates-calendar {
  grid-area: calendar;
  /* Events container - compact single container */
  /* Individual event items - compact layout */
  /* Date component with card-stack effect for multiple events */
  /* Event details */
  /* "See full calendar" link - use secondary button */
}
.highlighted-updates-calendar .calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}
.highlighted-updates-calendar .calendar-header h3 {
  margin: 0;
  color: #016449;
}
.highlighted-updates-calendar .calendar-header a {
  color: #9C2B32;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
}
.highlighted-updates-calendar .calendar-header a:hover {
  text-decoration: underline;
}
.highlighted-updates-calendar .calendar-events {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  margin-bottom: 1rem;
}
.highlighted-updates-calendar .calendar-event {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 0;
  text-decoration: none;
  color: inherit;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}
.highlighted-updates-calendar .calendar-event:last-child {
  border-bottom: none;
}
.highlighted-updates-calendar .calendar-event:hover {
  background: rgba(1, 100, 73, 0.05);
  margin: 0 -1rem;
  padding-left: 1rem;
  padding-right: 1rem;
  border-radius: 8px;
}
.highlighted-updates-calendar .event-date {
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  background: #016449;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;
  /* Card stack effect for multiple events */
}
.highlighted-updates-calendar .event-date.multiple-events::before {
  content: "";
  position: absolute;
  top: -3px;
  left: -3px;
  right: 3px;
  bottom: 3px;
  background: rgba(1, 100, 73, 0.3);
  border-radius: 8px;
  z-index: -1;
}
.highlighted-updates-calendar .event-date.multiple-events::after {
  content: "";
  position: absolute;
  top: -6px;
  left: -6px;
  right: 6px;
  bottom: 6px;
  background: rgba(1, 100, 73, 0.15);
  border-radius: 8px;
  z-index: -2;
}
.highlighted-updates-calendar .event-date span:first-child {
  font-size: 1.125rem;
  font-weight: 700;
  line-height: 1;
}
.highlighted-updates-calendar .event-date span:last-child {
  font-size: 0.625rem;
  font-weight: 500;
  text-transform: uppercase;
  opacity: 0.9;
}
.highlighted-updates-calendar .event-details {
  flex: 1;
  min-width: 0;
}
.highlighted-updates-calendar .event-details h4 {
  margin: 0 0 0.125rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.3;
  /* Truncate long titles */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.highlighted-updates-calendar .event-details .event-meta {
  display: flex;
  gap: 0.5rem;
}
.highlighted-updates-calendar .event-details .event-meta span {
  font-size: 0.75rem;
  color: #666;
}
.highlighted-updates-calendar .event-details .event-meta span:first-child {
  font-weight: 500;
  color: #016449;
}
.highlighted-updates-calendar .calendar-footer {
  text-align: center;
  padding: 8px 0;
}

/* News Carousel - Mobile-First Implementation */
.news-carousel {
  padding: 3rem 0;
  /* Header with navigation */
  /* Navigation buttons - Ultra simple with SVG arrows */
  /* Carousel wrapper - allows visible overflow for shadows/effects */
  /* Scrollable container */
  /* Card base styles */
  /* Card image */
  /* Card content */
  /* Archive card */
}
.news-carousel .container {
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 1rem;
}
.news-carousel .carousel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}
.news-carousel .carousel-header h3 {
  margin: 0;
  color: #016449;
  font-size: 1.5rem;
}
.news-carousel .carousel-header .carousel-nav {
  display: flex;
  flex-direction: row;
  gap: 0.5rem;
}
@media (max-width: 640px) {
  .news-carousel .carousel-header .carousel-nav {
    display: none; /* Hide on mobile, rely on touch scrolling */
  }
}
.news-carousel .carousel-btn {
  width: 44px;
  height: 44px;
  background: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  /* Override global button styles */
  padding: 0;
  box-shadow: none;
  transform: none;
  outline: none;
  /* SVG arrow icons */
  /* Previous arrow (left) */
  /* Next arrow (right) */
}
.news-carousel .carousel-btn:hover:not(:disabled) {
  background-color: #cfe7cb;
  color: #016449;
  box-shadow: none;
  transform: none;
}
.news-carousel .carousel-btn:active {
  box-shadow: none;
  transform: none;
}
.news-carousel .carousel-btn:focus {
  box-shadow: none;
  outline: none;
}
.news-carousel .carousel-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}
.news-carousel .carousel-btn::before {
  content: "";
  width: 16px;
  height: 16px;
  background-color: #000;
  transition: background-color 0.2s ease;
}
.news-carousel .carousel-btn:hover:not(:disabled)::before {
  background-color: #016449;
}
.news-carousel .carousel-btn--prev::before {
  -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z'/%3E%3C/svg%3E") no-repeat center;
          mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z'/%3E%3C/svg%3E") no-repeat center;
  -webkit-mask-size: contain;
          mask-size: contain;
}
.news-carousel .carousel-btn--next::before {
  -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z'/%3E%3C/svg%3E") no-repeat center;
          mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='currentColor'%3E%3Cpath d='M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z'/%3E%3C/svg%3E") no-repeat center;
  -webkit-mask-size: contain;
          mask-size: contain;
}
.news-carousel .carousel-wrapper {
  position: relative;
  overflow: visible; /* Allow content to overflow for shadows/effects */
  padding: 1rem 0;
}
.news-carousel .carousel-scroll {
  display: flex;
  gap: 1.5rem;
  overflow-x: auto;
  /* Note: overflow-y is automatically set to hidden when overflow-x is auto */
  scroll-behavior: smooth;
  scroll-snap-type: x mandatory;
  -webkit-overflow-scrolling: touch; /* iOS momentum scrolling */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
  padding: 1rem;
  margin: -1rem;
  /* Hide scrollbar in WebKit browsers */
}
.news-carousel .carousel-scroll::-webkit-scrollbar {
  display: none;
}
@media (max-width: 640px) {
  .news-carousel .carousel-scroll {
    gap: 1rem;
    padding: 1rem 0.5rem;
    margin: -1rem -0.5rem;
  }
}
.news-carousel .carousel-card {
  flex: 0 0 320px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  scroll-snap-align: start;
  transition: all 0.3s ease;
}
@media (max-width: 768px) {
  .news-carousel .carousel-card {
    flex: 0 0 280px;
  }
}
@media (max-width: 480px) {
  .news-carousel .carousel-card {
    flex: 0 0 260px;
  }
}
.news-carousel .carousel-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}
.news-carousel .carousel-card a {
  display: block;
  text-decoration: none;
  color: inherit;
  height: 100%;
}
.news-carousel .card-image {
  height: 200px;
  overflow: hidden;
  background: #f8f9fa;
  position: relative;
}
@media (max-width: 480px) {
  .news-carousel .card-image {
    height: 160px;
  }
}
.news-carousel .card-image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.news-carousel .card-image .card-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #016449;
  color: white;
}
.news-carousel .card-image .card-placeholder svg {
  opacity: 0.6;
}
.news-carousel .card-content {
  padding: 1.25rem;
}
@media (max-width: 480px) {
  .news-carousel .card-content {
    padding: 1rem;
  }
}
.news-carousel .card-content time {
  display: block;
  font-size: 0.75rem;
  color: #666;
  margin-bottom: 0.5rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}
.news-carousel .card-content h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.4;
  color: #1a1a1a;
  /* Limit to 2 lines */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.news-carousel .card-content p {
  margin: 0;
  font-size: 0.875rem;
  color: #666;
  line-height: 1.5;
  /* Limit to 3 lines */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.news-carousel .carousel-card--archive {
  background: #9C2B32;
  color: white;
}
.news-carousel .carousel-card--archive .archive-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 2rem 1.5rem;
  height: 100%;
  min-height: 300px;
}
@media (max-width: 480px) {
  .news-carousel .carousel-card--archive .archive-content {
    padding: 1.5rem 1rem;
    min-height: 260px;
  }
}
.news-carousel .carousel-card--archive .archive-icon {
  margin-bottom: 1rem;
  opacity: 0.9;
}
.news-carousel .carousel-card--archive .archive-icon svg {
  width: 48px;
  height: 48px;
}
.news-carousel .carousel-card--archive h4 {
  margin: 0 0 0.75rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
}
.news-carousel .carousel-card--archive p {
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
  opacity: 0.9;
  line-height: 1.5;
  color: white;
}
.news-carousel .carousel-card--archive .archive-cta {
  font-size: 0.875rem;
  font-weight: 500;
  opacity: 0.8;
  color: white;
}

.partners {
  margin-top: 2rem;
  padding: 3rem 0;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
}

.partners-header {
  text-align: center;
}

.partners-list {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
  gap: 3rem;
  margin-top: 2rem;
}
.partners-list .partner-name {
  font-weight: bold;
  font-size: 1.2rem;
}
.partners-list img {
  max-width: 150px;
  max-height: 60px;
  filter: grayscale(100%);
}

/*# sourceMappingURL=style.css.map*/